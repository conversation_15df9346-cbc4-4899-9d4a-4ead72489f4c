<template>
  <div class="app-container">
    <div class="inner">
      <el-main class="main">
        <el-row :gutter="24">
          <el-col
            :xs="24"
            :sm="7"
            style="margin-bottom: 10px"
            v-for="item in priceData"
            :key="item.symbol"
          >
            <el-card shadow="always">
              <el-row :gutter="24">
                <el-col :span="6">
                  <svg-icon
                    style="width: 48px; height: 48px"
                    :icon-class="item.svg"
                  />
                </el-col>
                <el-col :span="18">
                  <div>
                    {{ item.name }}
                  </div>
                  <div>
                    <span> ${{ item.price }} </span>
                  </div>

                  <el-tag
                    style="width: 90px; text-align: center"
                    v-if="item.persent >= 0"
                    type="success"
                  >
                    <i class="el-icon-caret-top"></i>
                    {{ item.persent }}%
                  </el-tag>
                  <el-tag
                    style="width: 90px; text-align: center"
                    v-else
                    type="danger"
                  >
                    <i class="el-icon-caret-bottom"></i>
                    {{ item.persent }}%
                  </el-tag>
                </el-col>
              </el-row>
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :xs="24" :sm="7" style="margin-bottom: 10px">
            <el-card shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("dashboard.简介") }}</span>
              </div>
              <p
                style="margin-top: 0px; letter-spacing: 0.5px; line-height: 1.3"
              >
                {{ $t("dashboard.描述") }}
              </p>
            </el-card>
          </el-col>
          <el-col :xs="24" :sm="7" style="margin-bottom: 10px">
            <el-card shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span> {{ $t("dashboard.实用工具") }}</span>
              </div>
              <el-row>
                <el-col :span="24">
                  <el-button size="small" plain>
                    <svg-icon
                      style="width: 14px; height: 14px"
                      icon-class="panda"
                    />
                    <a :href="pandaBridge" target="_blank">
                      {{ $t("dashboard.跨链闪兑") }}</a
                    >
                  </el-button>
                </el-col>
              </el-row>
              <el-row style="margin-top: 10px">
                <el-col :span="24">
                  <el-button size="small" plain>
                    <svg-icon
                      style="width: 14px; height: 14px"
                      icon-class="solana"
                    />
                    <a :href="solana" target="_blank">
                      {{ $t("dashboard.Solana发币") }}</a
                    >
                  </el-button>
                </el-col>
              </el-row>
              <el-row style="margin-top: 10px">
                <el-col :span="24">
                  <el-button size="small" plain>
                    <svg-icon
                      style="width: 14px; height: 14px"
                      icon-class="tron"
                    />
                    <a :href="tron" target="_blank">
                      {{ $t("dashboard.Tron发币") }}</a
                    >
                  </el-button>
                </el-col>
              </el-row>
              <el-row style="margin-top: 10px">
                <el-col :span="24">
                  <el-button size="small" plain>
                    <svg-icon
                      style="width: 14px; height: 14px"
                      icon-class="ton"
                    />
                    <a :href="ton" target="_blank">
                      {{ $t("dashboard.Ton发币") }}</a
                    >
                  </el-button>
                </el-col>
              </el-row>
              <el-row style="margin-top: 10px">
                <el-col :span="24">
                  <el-button size="small" plain>
                    <svg-icon
                      style="width: 14px; height: 14px"
                      icon-class="sui"
                    />
                    <a :href="sui" target="_blank">
                      {{ $t("dashboard.Sui发币") }}</a
                    >
                  </el-button>
                </el-col>
              </el-row>
            </el-card>
          </el-col>
          <el-col :xs="24" :sm="7" style="margin-bottom: 10px">
            <el-card shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("dashboard.第三方链接") }}</span>
              </div>
              <el-row>
                <el-col :span="24">
                  <el-button size="small" plain icon="el-icon-coin">
                    <a :href="pinkSell" target="_blank">
                      {{ $t("dashboard.粉红预售") }}</a
                    >
                  </el-button>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24" style="margin-top: 10px">
                  <el-button size="small" plain icon="el-icon-data-analysis">
                    <a :href="ave" target="_blank">
                      {{ $t("dashboard.AVE数据") }}</a
                    >
                  </el-button>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24" style="margin-top: 10px">
                  <el-button size="small" plain icon="el-icon-mall">
                    <a :href="biSell" target="_blank">
                      {{ $t("dashboard.币售商城") }}</a
                    >
                  </el-button>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24" style="margin-top: 10px">
                  <el-button size="small" plain icon="el-icon-news">
                    <a :href="blockweeks" target="_blank">
                      {{ $t("dashboard.区块周刊") }}</a
                    >
                  </el-button>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24" style="margin-top: 10px">
                  <el-button size="small" plain icon="el-icon-community">
                    <a :href="cryptoDriving" target="_blank">
                      {{ $t("dashboard.加密驱动社区") }}</a
                    >
                  </el-button>
                </el-col>
              </el-row>
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :xs="24" :sm="7" style="margin-bottom: 10px">
            <el-card shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("dashboard.帮助文档1") }}</span>
              </div>
              <el-row>
                <el-col :span="24" style="margin-top: 10px">
                  <el-button size="small" plain icon="el-icon-help">
                    <a :href="helpDocument" target="_blank">
                      {{ $t("dashboard.帮助文档") }}</a
                    >
                  </el-button>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24" style="margin-top: 10px">
                  <el-button size="small" plain icon="el-icon-youtube">
                    <a :href="youtube" target="_blank">
                      {{ $t("dashboard.Youtube演示视频") }}</a
                    >
                  </el-button>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24" style="margin-top: 10px">
                  <el-button size="small" plain icon="el-icon-data-analysis">
                    <a :href="dailymotion" target="_blank">
                      {{ $t("dashboard.Dailymotion演示视频") }}</a
                    >
                  </el-button>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24" style="margin-top: 10px">
                  <el-button size="small" plain icon="el-icon-data-analysis">
                    <a :href="tiktok" target="_blank">
                      {{ $t("dashboard.Tiktok演示视频") }}</a
                    >
                  </el-button>
                </el-col>
              </el-row>
            </el-card>
          </el-col>
          <el-col :xs="24" :sm="7" style="margin-bottom: 10px">
            <el-card shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("dashboard.开放与合作") }}</span>
              </div>

              <p style="margin-top: 0px; letter-spacing: 0.5px; line-height: 1">
                {{ $t("dashboard.p1") }}
              </p>
              <p style="letter-spacing: 0.5px; line-height: 1">
                {{ $t("dashboard.p2") }}
              </p>
            </el-card>
          </el-col>
          <el-col :xs="24" :sm="7" style="margin-bottom: 10px">
            <el-card shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("dashboard.联系我们") }}</span>
              </div>
              <el-row>
                <el-col :span="24">
                  <el-button size="small" plain icon="el-icon-email">
                    <a :href="emailLink" target="_blank"> {{ email }}</a>
                  </el-button>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24" style="margin-top: 10px">
                  <el-button size="small" plain icon="el-icon-telegram">
                    <a :href="telegramAdmin" target="_blank">
                      {{ $t("dashboard.商务咨询") }}</a
                    >
                  </el-button>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24" style="margin-top: 10px">
                  <el-button size="small" plain icon="el-icon-telegram">
                    <a :href="telegramGroup" target="_blank">
                      {{ $t("dashboard.官方电报交流群") }}</a
                    >
                  </el-button>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24" style="margin-top: 10px">
                  <el-button size="small" plain icon="el-icon-twitter">
                    <a :href="twitter" target="_blank">
                      {{ $t("dashboard.官方推特") }}</a
                    >
                  </el-button>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24" style="margin-top: 10px">
                  <el-button size="small" plain icon="el-icon-github">
                    <a :href="github" target="_blank">
                      {{ $t("dashboard.Github合约库") }}</a
                    >
                  </el-button>
                </el-col>
              </el-row>
            </el-card>
          </el-col>
        </el-row>
      </el-main>
    </div>
  </div>
</template>

<script>
export default {
  name: "Dashboard",
  data() {
    return {
      solana: "https://solana.pandatool.org/",
      tron: "https://tron.pandatool.org/",
      sui: "https://sui.pandatool.org/",
      ton: "https://ton.pandatool.org/",
      router1: "发币--LP分红",
      router2: "发币--LP分红+推荐(回转绑定)",
      router3: "市值管理",
      router4: "批量转账",

      telegramGroup:
        this.$i18n.locale == "zh-CN"
          ? "https://t.me/pandatool"
          : "https://t.me/pandatool_en",
      telegramAdmin: "https://t.me/btc6560",
      bili: "https://space.bilibili.com/3493104485140862",
      youtube: "https://www.youtube.com/@Panda_Tool/videos",
      dailymotion: "https://www.dailymotion.com/pandatool",
      tiktok: "https://www.tiktok.com/@pandatool.org",
      twitter: "https://twitter.com/PandaTool",
      github: "https://github.com/pandatoolcode",
      email: "<EMAIL>",
      emailLink: "mailto:<EMAIL>",
      helpDocument:
        this.$i18n.locale == "zh-CN"
          ? "https://help.pandatool.org"
          : "https://help.pandatool.org/english",
      cryptoDriving: "https://t.me/cryptodriving6",
      blockweeks: "https://www.blockweeks.com/",
      biSell:
        this.$i18n.locale == "zh-CN"
          ? "https://www.bisell.site/"
          : "https://en.bisell.site",
      ave: 'https://ave.ai/',
      pinkSell: "https://www.pinksale.finance/",
      pandaBridge: "https://bridge.pandatool.org/",
      lockReconnect: false, //是否真正建立连接
      timeout: 58 * 1000, //58秒一次心跳
      timeoutObj: null, //心跳心跳倒计时
      serverTimeoutObj: null, //心跳倒计时
      timeoutnum: null, //断开 重连倒计时
      task: null,
      priceData: [
        {
          name: "BTC / USDT",
          svg: "btc",
          symbol: "BTCUSDT",
          price: 0,
          persent: 0,
        },
        {
          name: "ETH / USDT",
          svg: "eth",
          symbol: "ETHUSDT",
          price: 0,
          persent: 0,
        },
        {
          name: "BNB / USDT",
          svg: "bnb",
          symbol: "BNBUSDT",
          price: 0,
          persent: 0,
        },
        {
          name: "DOGE / USDT",
          svg: "doge",
          symbol: "DOGEUSDT",
          price: 0,
          persent: 0,
        },
        {
          name: "SHIBA / USDT",
          svg: "shiba",
          symbol: "SHIBUSDT",
          price: 0,
          persent: 0,
        },
        {
          name: "SOL / USDT",
          svg: "sol",
          symbol: "SOLUSDT",
          price: 0,
          persent: 0,
        },
        {
          name: "TON / USDT",
          svg: "ton",
          symbol: "TONUSDT",
          price: 0,
          persent: 0,
        },
        {
          name: "ARB / USDT",
          svg: "arb",
          symbol: "ARBUSDT",
          price: 0,
          persent: 0,
        },
        {
          name: "TRX / USDT",
          svg: "tron",
          symbol: "TRXUSDT",
          price: 0,
          persent: 0,
        },
      ],
    };
  },
  computed: {},
  created() {
    this.initWebSocket();
    // this.currentTime();
  },
  destroyed() {
    this.websock.close(); //离开路由之后断开websocket连接
  },
  methods: {
    enterRouter(name) {
      if (name == "发币--LP分红") {
        this.$router.push({ path: "/coinrelease/LPReflection" });
      } else if (name == "发币--LP分红+推荐(回转绑定)") {
        this.$router.push({ path: "/coinrelease/LPwithInviter" });
      } else if (name == "批量转账") {
        console.log("piliang");
        this.$router.push({ path: "/multisend" });
      } else if (name == "市值管理") {
        this.$router.push({ path: "/buyAndSell" });
      } else {
        console.log("未完待续");
      }
    },
    currentTime() {
      // console.log("currentTime", this.formatDate);
      this.task = setInterval(() => {
        let actions = {
          id: "043a7cf2-bde3-4888-9604-c8ac41fcba4d",
          method: "ticker.24hr",
          params: {
            symbols: [
              "BTCUSDT",
              "ETHUSDT",
              "DOGEUSDT",
              "BNBUSDT",
              "SOLUSDT",
              "TONUSDT",
              "TRXUSDT",
              "ARBUSDT",
              "SHIBUSDT",
            ],
          },
        };
        this.websocketsend(JSON.stringify(actions));
      }, 4500);
    },
    initWebSocket() {
      //初始化weosocket
      const wsuri = "wss://ws-api.binance.com:443/ws-api/v3";
      this.websock = new WebSocket(wsuri);
      // 客户端接收服务端数据时触发
      this.websock.onmessage = this.websocketonmessage;
      // 连接建立时触发
      this.websock.onopen = this.websocketonopen;
      // 通信发生错误时触发
      this.websock.onerror = this.websocketonerror;
      // 连接关闭时触发
      this.websock.onclose = this.websocketclose;
    },
    // 连接建立时触发
    websocketonopen() {
      //开启心跳
      this.start();
      //连接建立之后执行send方法发送数据
    },
    // 通信发生错误时触发
    websocketonerror() {
      console.log("出现错误");
      this.reconnect();
    },
    // 客户端接收服务端数据时触发
    websocketonmessage(e) {
      let res = JSON.parse(e.data);
      if (res.status == 200) {
        for (let i = 0; i < res.result.length; i++) {
          for (let j = 0; j < this.priceData.length; j++) {
            if (this.priceData[j].symbol == res.result[i].symbol) {
              this.priceData[j].price = parseFloat(res.result[i].askPrice);
              this.priceData[j].persent = res.result[i].priceChangePercent;
            }
          }
        }
      }
      // console.log(res.result);
      //收到服务器信息，心跳重置
      this.reset();
    },
    websocketsend(Data) {
      //数据发送
      this.websock.send(Data);
    },
    // 连接关闭时触发
    websocketclose(e) {
      //关闭
      // console.log("断开连接", e);
      //重连
      this.reconnect();
    },
    reconnect() {
      //重新连接
      var that = this;
      if (that.lockReconnect) {
        return;
      }
      that.lockReconnect = true;
      //没连接上会一直重连，设置延迟避免请求过多
      that.timeoutnum && clearTimeout(that.timeoutnum);
      that.timeoutnum = setTimeout(function () {
        //新连接
        that.initWebSocket();
        that.lockReconnect = false;
      }, 5000);
    },
    reset() {
      //重置心跳
      var that = this;
      //清除时间
      clearTimeout(that.timeoutObj);
      clearTimeout(that.serverTimeoutObj);
      //重启心跳
      that.start();
    },
    start() {
      //开启心跳
      // console.log("开启心跳");
      var self = this;
      self.timeoutObj && clearTimeout(self.timeoutObj);
      self.serverTimeoutObj && clearTimeout(self.serverTimeoutObj);
      self.timeoutObj = setTimeout(function () {
        //这里发送一个心跳，后端收到后，返回一个心跳消息，
        if (self.websock.readyState == 1) {
          //如果连接正常
          // self.ws.send("heartCheck"); //这里可以自己跟后端约定
        } else {
          //否则重连
          self.reconnect();
        }
        self.serverTimeoutObj = setTimeout(function () {
          //超时关闭
          self.websock.close();
        }, self.timeout);
      }, self.timeout);
    },
  },
  mounted() {
    this.currentTime();
    // this.currentTime();
  },
  // 销毁定时器
  beforeDestroy() {
    if (this.task) {
      clearInterval(this.task); // 在Vue实例销毁前，清除时间定时器
    }
    this.websock.close();
  },
};
</script>

<style lang="scss" scoped>
.main {
  /* text-align: center; */
  padding-left: 12%;
  padding-right: 5%;
}
.box-card {
  height: 300px;
  // text-align: center;
}
</style>
